@props([
    'variant' => 'default', // default, success, warning, danger, info
    'size' => 'md', // sm, md, lg
    'rounded' => true
])

@php
    $baseClasses = 'inline-flex items-center font-medium whitespace-nowrap';

    $variants = [
        'default' => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
        'success' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
        'warning' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
        'danger' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
        'info' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
    ];

    $sizes = [
        'sm' => 'px-2 py-0.5 text-xs',
        'md' => 'px-2.5 py-0.5 text-sm',
        'lg' => 'px-3 py-1 text-base'
    ];

    $classes = $baseClasses . ' ' .
               ($variants[$variant] ?? $variants['default']) . ' ' .
               ($sizes[$size] ?? $sizes['md']) . ' ' .
               ($rounded ? 'rounded-full' : 'rounded');
@endphp

<span {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</span> 