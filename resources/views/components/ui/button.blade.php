@props([
    'type' => 'button',
    'variant' => 'primary', // primary, secondary, danger, success
    'size' => 'md', // sm, md, lg
    'loading' => false,
    'disabled' => false,
    'icon' => null,
    'iconPosition' => 'left', // left, right
    'href' => null
])

@php
    $baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-200 whitespace-nowrap';

    $variants = [
        'primary' => 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700',
        'secondary' => 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-blue-500',
        'danger' => 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 dark:bg-red-600 dark:hover:bg-red-700',
        'success' => 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 dark:bg-green-600 dark:hover:bg-green-700'
    ];

    $sizes = [
        'sm' => 'h-8 px-3 text-xs min-w-[5rem]',
        'md' => 'h-10 px-4 text-sm min-w-[6rem]',
        'lg' => 'h-12 px-6 text-base min-w-[7rem]'
    ];

    $classes = $baseClasses . ' ' .
               ($variants[$variant] ?? $variants['primary']) . ' ' .
               ($sizes[$size] ?? $sizes['md']) . ' ' .
               ($disabled ? 'opacity-50 cursor-not-allowed' : '') . ' ' .
               ($loading ? 'cursor-wait' : '');
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        <span class="inline-flex items-center gap-2">
            @if($loading)
                <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            @elseif($icon && $iconPosition === 'left')
                <span class="h-4 w-4">
                    {{ $icon }}
                </span>
            @endif

            {{ $slot }}

            @if($icon && $iconPosition === 'right')
                <span class="h-4 w-4">
                    {{ $icon }}
                </span>
            @endif
        </span>
    </a>
@else
    <button 
        type="{{ $type }}"
        {{ $attributes->merge(['class' => $classes]) }}
        @disabled($disabled || $loading)
    >
        <span class="inline-flex items-center gap-2">
            @if($loading)
                <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            @elseif($icon && $iconPosition === 'left')
                <span class="h-4 w-4">
                    {{ $icon }}
                </span>
            @endif

            {{ $slot }}

            @if($icon && $iconPosition === 'right')
                <span class="h-4 w-4">
                    {{ $icon }}
                </span>
            @endif
        </span>
    </button>
@endif 