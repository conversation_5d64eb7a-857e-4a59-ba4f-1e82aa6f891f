<div class="space-y-6">
    <!-- Header with Summary Cards -->
    <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Error Accounts Dashboard</h2>
            @if($readyToLoad && isset($errorSummary))
                <!-- Live Status Indicator -->
                <div class="flex items-center space-x-2">
                    @if(($errorSummary['by_severity']['critical'] ?? 0) > 0)
                        <div class="flex items-center px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full text-sm">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
                            {{ $errorSummary['by_severity']['critical'] }} Critical
                        </div>
                    @elseif(($errorSummary['total_errors'] ?? 0) > 0)
                        <div class="flex items-center px-3 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full text-sm">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                            {{ $errorSummary['total_errors'] }} Active
                        </div>
                    @else
                        <div class="flex items-center px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            All Clear
                        </div>
                    @endif
                </div>
            @endif
        </div>
        <div class="flex items-center space-x-2">
            <x-ui.button
                wire:click="toggleFilters"
                variant="secondary"
                size="sm"
                class="min-w-[7rem]">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
            </x-ui.button>

            <!-- Test Toast Button (remove in production) -->
            <x-ui.button
                onclick="testToast()"
                variant="danger"
                size="sm">
                Test Alert
            </x-ui.button>
        </div>
    </div>

    <!-- Error Summary -->
    @if($readyToLoad && isset($errorSummary))
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Error Summary</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">Last 24h</span>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($errorSummary['total_errors'] ?? 0) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Total Errors</div>
            </div>

            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ number_format($errorSummary['affected_accounts'] ?? 0) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Affected Accounts</div>
            </div>

            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">{{ number_format($errorSummary['by_severity']['critical'] ?? 0) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Critical</div>
            </div>

            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ number_format($errorSummary['by_category']['authentication'] ?? 0) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Auth Errors</div>
            </div>
        </div>
    </div>
    @endif

    <!-- Filters Panel -->
    @if($showFilters)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Filter Options</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">Refine your search</span>
        </div>
        <div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Category Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <svg class="w-4 h-4 mr-2 inline text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>Category
                    </label>
                    <select wire:model.live="filterCategory" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($this->getErrorCategoryOptions() as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Severity Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <svg class="w-4 h-4 mr-2 inline text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>Severity
                    </label>
                    <select wire:model.live="filterSeverity" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($this->getSeverityOptions() as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- VPS Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <svg class="w-4 h-4 mr-2 inline text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                        </svg>VPS
                    </label>
                    <select wire:model.live="filterVpsId" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="all">All VPS</option>
                        @if(isset($vpsList))
                            @foreach($vpsList as $vps)
                                <option value="{{ $vps->id }}">{{ $vps->name }}</option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <!-- Time Range Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <svg class="w-4 h-4 mr-2 inline text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>Time Range
                    </label>
                    <select wire:model.live="timeRange" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($this->getTimeRangeOptions() as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Group By -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <svg class="w-4 h-4 mr-2 inline text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>Group By
                    </label>
                    <select wire:model.live="groupBy" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($this->getGroupByOptions() as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <svg class="w-4 h-4 mr-2 inline text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>Search
                    </label>
                    <input type="text"
                           wire:model.live.debounce.300ms="search"
                           placeholder="Account, email, error message..."
                           class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
            </div>
        </div>

        <div class="mt-4 flex justify-between items-center">
            <x-ui.button
                wire:click="resetFilters"
                variant="secondary"
                size="sm">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Reset Filters
            </x-ui.button>

            @if(!empty($selectedAccounts))
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ count($selectedAccounts) }} selected</span>
                <x-ui.button
                    wire:click="bulkResolveErrors"
                    variant="success"
                    size="sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Bulk Resolve
                </x-ui.button>
            </div>
            @endif
        </div>
    </div>
    @endif

    <!-- Loading State -->
    @if(!$readyToLoad)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-8 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-300">Loading error accounts...</p>
    </div>
    @endif

    <!-- Error Accounts Content -->
    @if($readyToLoad && isset($errorAccounts))
        @if($errorAccounts->isEmpty())
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div class="text-gray-400 dark:text-gray-500 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Error Accounts Found</h3>
            <p class="text-gray-600 dark:text-gray-400">All accounts are functioning normally with the current filters.</p>
        </div>
        @else
            @foreach($errorAccounts as $groupName => $accounts)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                @if($groupBy !== 'none')
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white capitalize">
                        {{ str_replace('_', ' ', $groupName) }}
                        <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">({{ $accounts->count() }} accounts)</span>
                    </h3>
                </div>
                @endif

                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach($accounts as $account)
                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <!-- Account Header -->
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           wire:model="selectedAccounts"
                                           value="{{ $account->id }}"
                                           class="rounded border-gray-300 text-blue-600 mr-3">
                                    <div>
                                        <h4 class="font-medium text-gray-900 dark:text-white">{{ $account->login_name ?? 'Unknown' }}</h4>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $account->registered_email ?? 'No email' }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($account->latest_error->severity === 'critical')
                                        <x-ui.badge variant="danger" size="sm">Critical</x-ui.badge>
                                    @elseif($account->latest_error->severity === 'high')
                                        <x-ui.badge variant="warning" size="sm">High</x-ui.badge>
                                    @elseif($account->latest_error->severity === 'medium')
                                        <x-ui.badge variant="warning" size="sm">Medium</x-ui.badge>
                                    @else
                                        <x-ui.badge variant="default" size="sm">Low</x-ui.badge>
                                    @endif
                                </div>
                            </div>

                            <!-- Error Details -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm">
                                    <svg class="w-4 h-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                    <span class="text-gray-600 dark:text-gray-300">{{ $account->latest_error->error_message }}</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-gray-500 dark:text-gray-400 whitespace-nowrap">{{ $account->latest_error->occurred_at->diffForHumans() }}</span>
                                </div>
                                @if($account->latest_error->vps)
                                <div class="flex items-center text-sm">
                                    <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                                    </svg>
                                    <span class="text-gray-500 dark:text-gray-400">{{ $account->latest_error->vps->name }}</span>
                                </div>
                                @endif
                            </div>

                            <!-- Error Categories -->
                            <div class="flex flex-wrap gap-1 mb-4">
                                @foreach($account->error_categories as $category)
                                <x-ui.badge variant="info" size="sm">
                                    {{ ucfirst(str_replace('_', ' ', $category)) }}
                                </x-ui.badge>
                                @endforeach
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    @if($account->latest_error->screenshot_url)
                                    <a href="{{ $account->latest_error->screenshot_url }}"
                                       target="_blank"
                                       class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        Screenshot
                                    </a>
                                    @endif
                                    <button wire:click="retryAccountLogin({{ $account->id }})"
                                            class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200 text-sm flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        Retry Login
                                    </button>
                                </div>
                                <x-ui.button
                                    wire:click="resolveError({{ $account->latest_error->id }})"
                                    variant="success"
                                    size="sm">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Resolve
                                </x-ui.button>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endforeach
        @endif
    @endif
</div>

@push('scripts')
<script>
    // Define functions first to avoid reference errors
    let toastContainer = null;
    let toastCount = 0;

    function createToastContainer() {
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
            toastContainer.style.pointerEvents = 'none';
            document.body.appendChild(toastContainer);
        }
        return toastContainer;
    }

    function showToast(message, type = 'info', duration = 5000) {
        const container = createToastContainer();
        toastCount++;

        const toast = document.createElement('div');
        toast.className = `px-6 py-4 rounded-lg shadow-lg text-white transition-all duration-300 transform translate-x-full opacity-0 max-w-sm ${
            type === 'success' ? 'bg-green-500' :
            type === 'warning' ? 'bg-yellow-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'critical' ? 'bg-red-600 border-2 border-red-300' : 'bg-blue-500'
        }`;
        toast.style.pointerEvents = 'auto';

        // Create toast content
        const content = document.createElement('div');
        content.className = 'flex items-center';

        const icon = document.createElement('i');
        icon.className = `fas mr-3 ${
            type === 'success' ? 'fa-check-circle' :
            type === 'warning' ? 'fa-exclamation-triangle' :
            type === 'error' || type === 'critical' ? 'fa-exclamation-circle' : 'fa-info-circle'
        }`;

        const text = document.createElement('span');
        text.textContent = message;
        text.className = 'flex-1';

        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.className = 'ml-3 text-white hover:text-gray-200 transition-colors';
        closeBtn.onclick = () => removeToast(toast);

        content.appendChild(icon);
        content.appendChild(text);
        content.appendChild(closeBtn);
        toast.appendChild(content);

        container.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 10);

        // Auto-remove
        const timeout = setTimeout(() => {
            removeToast(toast);
        }, duration);

        // Store timeout for manual removal
        toast._timeout = timeout;

        return toast;
    }

    function removeToast(toast) {
        if (toast._timeout) {
            clearTimeout(toast._timeout);
        }

        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
                toastCount--;

                // Remove container if empty
                if (toastCount === 0 && toastContainer) {
                    document.body.removeChild(toastContainer);
                    toastContainer = null;
                }
            }
        }, 300);
    }

    // Test function for the test button
    function testToast() {
        showToast('🚨 Test critical error alert!', 'critical');
    }

    // Show critical error alerts immediately
    function showCriticalAlert(errorData) {
        showToast(
            `🚨 Critical Error: ${errorData.error_message}`,
            'critical',
            10000 // 10 seconds for critical errors
        );
    }

    // Real-time updates via WebSocket/Broadcasting
    if (typeof Echo !== 'undefined') {
        Echo.channel('error-accounts')
            .listen('.error.created', (e) => {
                // Show appropriate toast based on severity
                const severity = e.severity || 'medium';
                const toastType = severity === 'critical' ? 'critical' :
                                 severity === 'high' ? 'error' : 'warning';

                if (severity === 'critical') {
                    showCriticalAlert(e);
                } else {
                    showToast(
                        `New ${severity} error: ${e.error_message}`,
                        toastType,
                        severity === 'high' ? 8000 : 5000
                    );
                }

                // Refresh the component data
                @this.call('refreshData');
            })
            .listen('.error.resolved', (e) => {
                // Show success notification
                showToast('✅ Error resolved for account: ' + (e.account?.login_name || 'Unknown'), 'success');
                // Refresh the component data
                @this.call('refreshData');
            });
    }

    // Fallback: Poll for updates every 30 seconds
    setInterval(() => {
        @this.call('refreshData');
    }, 30000);

    // Functions already defined above - no duplicates needed

    // Event handlers
    window.addEventListener('error-resolved', event => {
        showToast('Error resolved successfully', 'success');
    });

    window.addEventListener('bulk-errors-resolved', event => {
        showToast('Multiple errors resolved successfully', 'success');
    });

    window.addEventListener('retry-account-login', event => {
        showToast('Login retry initiated for account', 'info');
    });

    window.addEventListener('export-error-report', event => {
        showToast('Error report export started', 'info');
    });

    window.addEventListener('error-account-updated', event => {
        const { type, data } = event.detail;
        if (type === 'new') {
            const severity = data.severity || 'medium';
            const toastType = severity === 'critical' ? 'critical' :
                             severity === 'high' ? 'error' : 'warning';

            if (severity === 'critical') {
                showCriticalAlert(data);
            } else {
                showToast(
                    `📢 New ${severity} error: ${data.error_message}`,
                    toastType,
                    severity === 'high' ? 8000 : 5000
                );
            }
        } else if (type === 'resolved') {
            showToast('✅ Error resolved successfully', 'success');
        }
    });

    // Filter integration with alerts
    window.addEventListener('filter-by-severity', event => {
        @this.set('filterSeverity', event.detail.severity);
        @this.set('showFilters', true);
    });

    window.addEventListener('filter-by-category', event => {
        @this.set('filterCategory', event.detail.category);
        @this.set('showFilters', true);
    });

    window.addEventListener('filter-by-vps', event => {
        @this.set('filterVpsId', event.detail.vpsId);
        @this.set('showFilters', true);
    });
</script>
@endpush
