{{-- Enhanced Command Card with Navigation Links --}}
<div wire:key="command-{{ $command->id }}"
     data-command-id="{{ $command->id }}"
     class="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 {{ $command->status === 'pending' ? 'opacity-75' : '' }}">

    <div class="flex items-start gap-4">
        {{-- Selection Checkbox --}}
        <div class="flex items-center pt-1">
            <input type="checkbox"
                   wire:model.live="selectedCommands"
                   value="{{ $command->id }}"
                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </div>

        {{-- Command Content --}}
        <div class="flex-1 min-w-0">
            {{-- Header Row --}}
            <div class="flex items-center flex-wrap gap-2 mb-2">
                {{-- Persona Name with Navigation Link --}}
                @if($command->persona)
                    <a href="{{ route('personas.show', $command->persona) }}"
                       class="font-semibold text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors">
                        {{ $command->persona->first_name }} {{ $command->persona->last_name }}
                    </a>
                @else
                    <span class="font-semibold text-gray-500 dark:text-gray-400">
                        {{ $command->command_params['original_persona_id'] ?? 'Unknown Persona' }}
                    </span>
                @endif

                {{-- Twitter ID with Navigation Link --}}
                @if($command->account?->account_id)
                    <a href="{{ route('accounts.show', $command->account) }}"
                       class="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 transition-colors">
                        ({{ $command->account->account_id }})
                    </a>
                @elseif($command->account?->login_name)
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                        ({{ $command->account->login_name }})
                    </span>
                @endif

                {{-- Status Badge --}}
                @php
                    $diffInSeconds = $command->updated_at ? $command->updated_at->diffInSeconds() : null;
                    $statusVariant = match($command->status) {
                        'pending' => $diffInSeconds && $diffInSeconds < 10 ? 'success' :
                                   ($diffInSeconds && $diffInSeconds < 30 ? 'warning' : 'danger'),
                        'processing' => 'info',
                        'failed', 'fail', 'error' => 'danger',
                        'success' => 'success',
                        default => 'default'
                    };
                @endphp

                <x-ui.tooltip :text="ucfirst($command->status) . ($command->completed_at ? ' at ' . $command->completed_at->format('H:i:s') : '')">
                    <x-ui.badge :variant="$statusVariant" size="sm" class="status-badge">
                        {{ ucfirst($command->status) }}
                    </x-ui.badge>
                </x-ui.tooltip>

                {{-- Time Information --}}
                <x-ui.tooltip text="Created: {{ $command->created_at->format('M j, Y H:i:s') }}">
                    <span class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                        {{ $command->created_at->diffForHumans(['short' => true]) }}
                    </span>
                </x-ui.tooltip>

                {{-- Execution Time --}}
                @if($command->started_at && $command->completed_at)
                    <x-ui.tooltip text="Execution time">
                        <span class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                            ({{ $command->started_at->diffInSeconds($command->completed_at) }}s)
                        </span>
                    </x-ui.tooltip>
                @endif
            </div>

            {{-- Command Details Row --}}
            <div class="flex items-center gap-3 mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ ucfirst($command->type) }}
                </span>

                <span class="text-sm text-gray-500 dark:text-gray-400">
                    {{ ucfirst($command->target_network) }}
                </span>

                {{-- VPS Information with Link --}}
                @if($command->persona?->vps)
                    <x-ui.tooltip text="VPS: {{ $command->persona->vps->name ?? 'Unknown' }}">
                        <span class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded">
                            {{ $command->persona->vps->external_ip }}
                        </span>
                    </x-ui.tooltip>
                @endif

                {{-- Priority Indicator --}}
                @if($command->priority && $command->priority > 0)
                    <x-ui.tooltip text="Priority: {{ $command->priority }}">
                        <span class="text-xs px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">
                            P{{ $command->priority }}
                        </span>
                    </x-ui.tooltip>
                @endif
            </div>

            {{-- Error Message --}}
            @if($command->error_message)
                <div class="mt-2 p-3 rounded-md {{ $command->hasFixedMissingRelationships() ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800' }}">
                    <p class="text-sm {{ $command->hasFixedMissingRelationships() ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300' }}">
                        <span class="font-medium">
                            {{ $command->hasFixedMissingRelationships() ? '✓ Fixed:' : 'Error:' }}
                        </span>
                        {{ $command->getFormattedErrorMessage() }}
                    </p>
                </div>
            @endif

            {{-- Command Parameters Preview --}}
            @if($command->command_params && is_array($command->command_params))
                <div class="mt-2">
                    <details class="group">
                        <summary class="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-200">
                            View Parameters
                        </summary>
                        <div class="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs font-mono text-gray-600 dark:text-gray-300 max-h-32 overflow-y-auto">
                            @foreach($command->command_params as $key => $value)
                                <div><span class="text-gray-500">{{ $key }}:</span> {{ is_string($value) ? Str::limit($value, 50) : json_encode($value) }}</div>
                            @endforeach
                        </div>
                    </details>
                </div>
            @endif

            {{-- Screenshot --}}
            @if($command->screenshot_path)
                <div class="mt-3">
                    <img src="{{ Storage::disk('public')->url($command->screenshot_path) }}"
                         alt="Command Result Screenshot"
                         class="w-20 h-auto rounded-md shadow-sm cursor-pointer hover:shadow-md transition-shadow duration-200"
                         @click="openModal('{{ Storage::disk('public')->url($command->screenshot_path) }}')"
                         loading="lazy">
                </div>
            @endif
        </div>

        {{-- Actions Column --}}
        <div class="flex flex-col gap-2">
            {{-- Context Menu --}}
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open"
                        class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                </button>

                <div x-show="open"
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10">

                    <div class="py-1">
                        @if($command->persona)
                            <a href="{{ route('personas.show', $command->persona) }}"
                               class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                View Persona
                            </a>
                        @endif

                        @if($command->account)
                            <a href="{{ route('accounts.show', $command->account) }}"
                               class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                View Account
                            </a>
                        @endif

                        @if($command->persona?->vps)
                            <a href="{{ route('vps.show', $command->persona->vps) }}"
                               class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                View VPS
                            </a>
                        @endif

                        @if($command->logs)
                            <a href="{{ route('commands.logs', $command->id) }}"
                               class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                View Logs
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            {{-- Primary Actions --}}
            <div class="flex flex-col gap-1">
                @if($command->isRetryable())
                    <x-ui.button
                        wire:click="retryCommand({{ $command->id }})"
                        wire:loading.attr="disabled"
                        wire:target="retryCommand({{ $command->id }})"
                        variant="success"
                        size="sm">
                        <div wire:loading.remove wire:target="retryCommand({{ $command->id }})">
                            Retry
                        </div>
                        <x-ui.loading-state type="inline" show wire:loading wire:target="retryCommand({{ $command->id }})" />
                    </x-ui.button>
                @endif

                <x-ui.button
                    wire:click="deleteCommand({{ $command->id }})"
                    wire:loading.attr="disabled"
                    wire:target="deleteCommand({{ $command->id }})"
                    variant="danger"
                    size="sm">
                    <div wire:loading.remove wire:target="deleteCommand({{ $command->id }})">
                        Delete
                    </div>
                    <x-ui.loading-state type="inline" show wire:loading wire:target="deleteCommand({{ $command->id }})" />
                </x-ui.button>
            </div>
        </div>
    </div>
</div>
