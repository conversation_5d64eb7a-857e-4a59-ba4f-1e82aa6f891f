<?php
use Illuminate\Support\Facades\Storage;
?>

<div class="space-y-6" 
     wire:poll.10s="loadMissingAccounts"
     wire:init="deferredLoad">
    <div class="relative">
        <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="deferredLoad" message="Loading accounts...">
            Loading accounts...
        </x-ui.loading-state>

        {{-- Header --}}
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Missing Accounts</h2>
                <x-ui.tooltip text="Live updates every 10 seconds">
                    <x-ui.badge variant="info" size="sm">Live</x-ui.badge>
                </x-ui.tooltip>
            </div>
            <div class="flex items-center gap-3">
                <select wire:model.live="filterType"
                        class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                        wire:loading.attr="disabled"
                        wire:target="filterType"
                        aria-label="Filter by command type">
                    <option value="">All Types</option>
                    <option value="reply">Reply</option>
                    <option value="post">Post</option>
                    <option value="login">Login</option>
                </select>
                <x-ui.button
                    wire:click="loadMissingAccounts"
                    wire:loading.attr="disabled"
                    wire:target="loadMissingAccounts"
                    variant="secondary"
                    size="sm"
                    class="min-w-[5rem]">
                    <div wire:loading.remove wire:target="loadMissingAccounts">
                        Refresh
                    </div>
                    <x-ui.loading-state type="inline" show wire:loading wire:target="loadMissingAccounts" />
                </x-ui.button>
                <x-ui.button
                    wire:click="retryAllAccounts"
                    wire:loading.attr="disabled"
                    wire:target="retryAllAccounts"
                    variant="success"
                    size="sm"
                    class="min-w-[5rem]">
                    <div wire:loading.remove wire:target="retryAllAccounts">
                        Retry All
                    </div>
                    <x-ui.loading-state type="inline" show wire:loading wire:target="retryAllAccounts" />
                </x-ui.button>
            </div>
        </div>

        {{-- List View --}}
        @if (!$readyToLoad)
            <div class="space-y-4">
                <x-ui.loading-state type="skeleton" repeat="3" />
            </div>
        @else
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($commands as $command)
                        <li wire:key="command-{{ $command->id }}"
                            class="border-b border-gray-200 dark:border-gray-700 last:border-0 hover:bg-gray-50 dark:hover:bg-gray-700/50 px-4 py-4">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2">
                                            <span class="font-medium text-gray-900 dark:text-white">
                                                {{ $command->command_params['original_persona_id'] ?? 'Unknown ID' }}
                                            </span>
                                            <x-ui.badge variant="info" size="sm">
                                                {{ $command->type }}
                                            </x-ui.badge>
                                            <span class="text-sm text-gray-500 dark:text-gray-400 whitespace-nowrap">
                                                {{ $command->created_at->diffForHumans() }}
                                            </span>
                                        </div>
                                        
                                        @if($command->message)
                                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                                                {{ $command->message }}
                                            </p>
                                        @endif

                                        @if($command->error_message)
                                            <p class="mt-1 text-sm {{ $command->hasFixedMissingRelationships() ? 'text-green-600 dark:text-green-400 font-medium' : 'text-red-600 dark:text-red-400' }}">
                                                {{ $command->getFormattedErrorMessage() }}
                                                @if($command->hasFixedMissingRelationships())
                                                    <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                @endif
                                            </p>
                                        @endif
                                    </div>
                                    
                                    <div class="flex items-center gap-2 ml-4">
                                        <x-ui.button 
                                            wire:click="openSetupModal({{ $command->id }})"
                                            wire:loading.attr="disabled"
                                            wire:target="openSetupModal({{ $command->id }})"
                                            variant="primary"
                                            size="sm">
                                            <div wire:loading.remove wire:target="openSetupModal({{ $command->id }})">
                                                Setup Account
                                            </div>
                                            <x-ui.loading-state type="inline" show wire:loading wire:target="openSetupModal({{ $command->id }})" />
                                        </x-ui.button>

                                        <x-ui.button 
                                            wire:click="deleteAccount({{ $command->id }})"
                                            wire:loading.attr="disabled"
                                            wire:target="deleteAccount({{ $command->id }})"
                                            variant="danger"
                                            size="sm">
                                            <div wire:loading.remove wire:target="deleteAccount({{ $command->id }})">
                                                Delete
                                            </div>
                                            <x-ui.loading-state type="inline" show wire:loading wire:target="deleteAccount({{ $command->id }})" />
                                        </x-ui.button>
                                    </div>
                                </div>

                                <div class="mt-2 sm:flex sm:justify-between">
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 gap-4">
                                        <div class="flex items-center gap-1">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            <span>Attempts: {{ $command->attempts }}</span>
                                        </div>
                                        @if($command->failed_at)
                                            <div class="flex items-center gap-1">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <span>Failed: {{ $command->failed_at->diffForHumans() }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </li>
                    @empty
                        <li class="px-4 py-12 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No missing accounts</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                {{ $filterType ? 'Try changing your filter' : 'All accounts are properly configured' }}
                            </p>
                            <div class="mt-6">
                                <x-ui.button 
                                    wire:click="loadMissingAccounts" 
                                    wire:loading.attr="disabled"
                                    wire:target="loadMissingAccounts"
                                    variant="secondary"
                                    size="sm">
                                    <div wire:loading.remove wire:target="loadMissingAccounts">
                                        Refresh List
                                    </div>
                                    <x-ui.loading-state type="inline" show wire:loading wire:target="loadMissingAccounts" />
                                </x-ui.button>
                            </div>
                        </li>
                    @endforelse
                </ul>
            </div>
        @endif
    </div>

    {{-- Setup Modal --}}
    <div
        x-data="{ show: @entangle('showSetupModal') }"
        x-show="show"
        x-cloak
        class="fixed inset-0 overflow-y-auto px-4 py-6 sm:px-0 z-50"
        style="display: none;">
        
        {{-- Modal Backdrop --}}
        <div class="fixed inset-0 transform transition-all" x-show="show">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>

        {{-- Modal Content --}}
        <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-full sm:max-w-lg sm:mx-auto">
            <form wire:submit.prevent="createPersonaWithAccount" class="space-y-4">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Setup Account</h2>
                        <button type="button" wire:click="$set('showSetupModal', false)" class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400">
                            <span class="sr-only">Close</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    @if($isLoading)
                        <div class="py-8 text-center">
                            <x-ui.loading-state type="inline" show="true" />
                            <p class="mt-2 text-sm text-gray-600">Loading account information...</p>
                        </div>
                    @else
                        @if($apiError)
                            <div class="p-4 bg-red-50 text-red-700 rounded-lg">
                                <p class="text-sm">{{ $apiError }}</p>
                            </div>
                        @endif

                        {{-- Twitter User ID (Read-only) --}}
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">
                                Twitter User ID
                            </label>
                            <input 
                                type="text" 
                                value="{{ $personaData['user_id'] }}"
                                class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 text-gray-500"
                                disabled>
                        </div>

                        {{-- Persona Information Section --}}
                        <div class="bg-gray-50 p-4 rounded-lg space-y-4 mb-4">
                            <h3 class="font-medium text-gray-900">Persona Information</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-gray-700">
                                        First Name
                                    </label>
                                    <input 
                                        type="text" 
                                        id="first_name" 
                                        wire:model="personaData.first_name"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                    @error('personaData.first_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-gray-700">
                                        Last Name
                                    </label>
                                    <input 
                                        type="text" 
                                        id="last_name" 
                                        wire:model="personaData.last_name"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                    @error('personaData.last_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700">
                                        Email
                                    </label>
                                    <input 
                                        type="email" 
                                        id="email" 
                                        wire:model="personaData.email"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                    @error('personaData.email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700">
                                        Timezone
                                    </label>
                                    <input 
                                        type="text" 
                                        id="timezone" 
                                        wire:model="personaData.timezone"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('personaData.timezone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="country" class="block text-sm font-medium text-gray-700">
                                        Country
                                    </label>
                                    <input 
                                        type="text" 
                                        id="country" 
                                        wire:model="personaData.country"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                    @error('personaData.country')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="city" class="block text-sm font-medium text-gray-700">
                                        City
                                    </label>
                                    <input 
                                        type="text" 
                                        id="city" 
                                        wire:model="personaData.city"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('personaData.city')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="col-span-2">
                                    <label for="vps_id" class="block text-sm font-medium text-gray-700">
                                        VPS
                                    </label>
                                    <select 
                                        id="vps_id" 
                                        wire:model="personaData.vps_id"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                        <option value="">Select VPS</option>
                                        @foreach($availableVps as $vps)
                                            <option value="{{ $vps->id }}">{{ $vps->name }} ({{ $vps->external_ip }})</option>
                                        @endforeach
                                    </select>
                                    @error('personaData.vps_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- Account Information Section --}}
                        <div class="bg-gray-50 p-4 rounded-lg space-y-4">
                            <h3 class="font-medium text-gray-900">Account Information</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="col-span-2">
                                    <label for="account_type" class="block text-sm font-medium text-gray-700">
                                        Account Type
                                    </label>
                                    <select 
                                        id="account_type" 
                                        wire:model.live="accountData.account_type" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        disabled>
                                        <option value="twitter">Twitter</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="login_name" class="block text-sm font-medium text-gray-700">
                                        Login Username
                                    </label>
                                    <input 
                                        type="text" 
                                        id="login_name" 
                                        wire:model="accountData.login_name"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                    @error('accountData.login_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="login_password" class="block text-sm font-medium text-gray-700">
                                        Password
                                    </label>
                                    <input 
                                        type="password" 
                                        id="login_password" 
                                        wire:model="accountData.login_password"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        required>
                                    @error('accountData.login_password')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="registered_email" class="block text-sm font-medium text-gray-700">
                                        Registered Email
                                    </label>
                                    <input 
                                        type="email" 
                                        id="registered_email" 
                                        wire:model="accountData.registered_email"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('accountData.registered_email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="registered_phone" class="block text-sm font-medium text-gray-700">
                                        Registered Phone
                                    </label>
                                    <input 
                                        type="text" 
                                        id="registered_phone" 
                                        wire:model="accountData.registered_phone"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('accountData.registered_phone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- Form Actions --}}
                        <div class="flex justify-end gap-2 pt-4">
                            <x-ui.button 
                                wire:click="$set('showSetupModal', false)"
                                wire:loading.attr="disabled"
                                variant="secondary"
                                size="sm">
                                Cancel
                            </x-ui.button>
                            <x-ui.button 
                                type="submit"
                                wire:loading.attr="disabled"
                                wire:target="createPersonaWithAccount"
                                variant="primary"
                                size="sm">
                                <div wire:loading.remove wire:target="createPersonaWithAccount">
                                    Create Account
                                </div>
                                <x-ui.loading-state type="inline" show wire:loading wire:target="createPersonaWithAccount" />
                            </x-ui.button>
                        </div>
                    @endif
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('livewire:init', () => {
    Livewire.on('accountDeleted', () => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: 'Account deleted successfully', type: 'success' }
        }));
    });

    Livewire.on('accountRetried', () => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: 'Account queued for retry', type: 'success' }
        }));
    });

    Livewire.on('allAccountsRetried', () => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: 'All accounts queued for retry', type: 'success' }
        }));
    });

    Livewire.on('error', (message) => {
        window.dispatchEvent(new CustomEvent('notify', { 
            detail: { message: message, type: 'error' }
        }));
    });
});
</script>
@endpush 