@php
    use Illuminate\Support\Facades\Storage;
@endphp

<div class="space-y-6"
     x-data="{
        showModal: false,
        modalImage: '',
        selectedCommands: @entangle('selectedCommands'),
        selectAll: @entangle('selectAll'),
        showFilters: @entangle('showFilters'),
        openModal(url) {
            this.modalImage = url;
            this.showModal = true;
            document.body.style.overflow = 'hidden';
        },
        closeModal() {
            this.showModal = false;
            document.body.style.overflow = '';
        },
        updateCommandStatus(data) {
            // Update specific command status in real-time
            const commandElement = document.querySelector(`[data-command-id='${data.commandId}']`);
            if (commandElement) {
                const statusBadge = commandElement.querySelector('.status-badge');
                if (statusBadge) {
                    statusBadge.textContent = data.status;
                    statusBadge.className = `status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClasses(data.status)}`;
                }
            }
        }
     }"
     @commandsRefreshed.window="console.log('Commands refreshed')"
     @updateCommandStatus.window="updateCommandStatus($event.detail)"
     @bulkRetried.window="$dispatch('notify', { message: `${$event.detail.count} commands retried`, type: 'success' })"
     @bulkDeleted.window="$dispatch('notify', { message: `${$event.detail.count} commands deleted`, type: 'success' })">
    {{-- Modal for full-size screenshot --}}
    <x-ui.modal :show="false" x-model="showModal" max-width="4xl">
        <div class="relative">
            <img :src="modalImage"
                 alt="Command Screenshot"
                 class="w-full h-auto rounded-lg shadow-lg"
                 loading="lazy">
        </div>
    </x-ui.modal>

    {{-- Header --}}
    <div class="space-y-4 mb-6">
        {{-- Title and Status Row --}}
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Recent Commands</h2>
                <div class="flex items-center gap-3">
                    <x-ui.tooltip text="Real-time updates via WebSocket">
                        <x-ui.badge variant="success" size="sm">
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                Live
                            </div>
                        </x-ui.badge>
                    </x-ui.tooltip>
                    @if($lastUpdate)
                        <x-ui.tooltip text="Last updated: {{ $lastUpdate->format('H:i:s') }}">
                            <span class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                                {{ $lastUpdate->diffForHumans() }}
                            </span>
                        </x-ui.tooltip>
                    @endif
                </div>
            </div>

            {{-- Action Buttons --}}
            <div class="flex items-center gap-2">
                <x-ui.button
                    wire:click="toggleFilters"
                    variant="secondary"
                    size="sm"
                    class="min-w-[7rem]">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    {{ $showFilters ? 'Hide' : 'Show' }} Filters
                </x-ui.button>

                <x-ui.button
                    wire:click="refreshCommands"
                    wire:loading.attr="disabled"
                    wire:target="refreshCommands"
                    variant="secondary"
                    size="sm"
                    class="min-w-[5rem]">
                    <div wire:loading.remove wire:target="refreshCommands">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Refresh
                    </div>
                    <x-ui.loading-state type="inline" show wire:loading wire:target="refreshCommands" />
                </x-ui.button>
            </div>
        </div>

        {{-- Search Row --}}
        <div class="flex justify-start">
            <div class="relative w-full max-w-md">
                <input type="text"
                       wire:model.live.debounce.300ms="search"
                       placeholder="Search commands..."
                       class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500">
                <svg class="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                @if($search)
                    <button wire:click="$set('search', '')" class="absolute right-2 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                @endif
            </div>
        </div>
    </div>

    {{-- Enhanced Filters --}}
    <div x-show="showFilters"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {{-- Status Filter --}}
            <div>
                <label for="filterStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                <select id="filterStatus" wire:model.live="filterStatus"
                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="success">Success</option>
                    <option value="failed">Failed</option>
                    <option value="error">Error</option>
                </select>
            </div>

            {{-- Type Filter --}}
            <div>
                <label for="filterType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                <select id="filterType" wire:model.live="filterType"
                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
                    <option value="">All Types</option>
                    <option value="login">Login</option>
                    <option value="post">Post</option>
                    <option value="reply">Reply</option>
                </select>
            </div>

            {{-- Persona Filter --}}
            <div>
                <label for="filterPersonaId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Persona</label>
                <select id="filterPersonaId" wire:model.live="filterPersonaId"
                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
                    <option value="">All Personas</option>
                    @foreach($personaOptions as $persona)
                        <option value="{{ $persona->id }}">{{ $persona->name }}</option>
                    @endforeach
                </select>
            </div>

            {{-- Date Range Filter --}}
            <div>
                <label for="dateRange" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Period</label>
                <select id="dateRange" wire:model.live="dateRange"
                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
                    <option value="1h">Last Hour</option>
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                </select>
            </div>
        </div>

        {{-- Group By and Bulk Actions --}}
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center gap-4">
                <div>
                    <label for="groupBy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group By</label>
                    <select id="groupBy" wire:model.live="groupBy"
                            class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
                        <option value="none">No Grouping</option>
                        <option value="status">By Status</option>
                        <option value="type">By Type</option>
                        <option value="persona">By Persona</option>
                    </select>
                </div>
            </div>

            {{-- Bulk Actions --}}
            <div class="flex items-center gap-2" x-show="selectedCommands.length > 0">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                    <span x-text="selectedCommands.length"></span> selected
                </span>
                <x-ui.button wire:click="retrySelected" variant="success" size="sm">
                    Retry Selected
                </x-ui.button>
                <x-ui.button wire:click="deleteSelected" variant="danger" size="sm">
                    Delete Selected
                </x-ui.button>
            </div>
        </div>
    </div>

    {{-- Commands Count Summary --}}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Command Summary</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">
                {{ ucfirst(str_replace(['1h', '24h', '7d', '30d'], ['Last Hour', 'Last 24h', 'Last 7 Days', 'Last 30 Days'], $dateRange)) }}
            </span>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            @php
                $totalSuccess = array_sum(array_map(fn($counts) => $counts['success'] ?? 0, $commandsCount->toArray()));
                $totalFailed = array_sum(array_map(fn($counts) => $counts['failed'] ?? 0, $commandsCount->toArray()));
                $totalPending = array_sum(array_map(fn($counts) => $counts['pending'] ?? 0, $commandsCount->toArray()));
                $totalCommands = $totalSuccess + $totalFailed + $totalPending;
            @endphp

            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalCommands) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Total Commands</div>
            </div>

            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ number_format($totalSuccess) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Successful</div>
            </div>

            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">{{ number_format($totalFailed) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Failed</div>
            </div>

            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ number_format($totalPending) }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Pending</div>
            </div>
        </div>

        {{-- Removed detailed breakdown by persona as it becomes too large with many accounts --}}
    </div>

    {{-- Commands List --}}
    <div wire:key="commands-list">
        {{-- Bulk Selection Header --}}
        @if(count($commands) > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <label class="flex items-center">
                            <input type="checkbox"
                                   wire:model.live="selectAll"
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Select All ({{ count($commands) }} commands)
                            </span>
                        </label>

                        <div x-show="selectedCommands.length > 0" class="flex items-center gap-2">
                            <span class="text-sm text-gray-600 dark:text-gray-400">
                                <span x-text="selectedCommands.length"></span> selected
                            </span>
                            <x-ui.button wire:click="retrySelected" variant="success" size="sm">
                                Retry Selected
                            </x-ui.button>
                            <x-ui.button wire:click="deleteSelected" variant="danger" size="sm">
                                Delete Selected
                            </x-ui.button>
                        </div>
                    </div>

                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            Showing {{ count($commands) }} commands
                        </span>
                    </div>
                </div>
            </div>
        @endif

        {{-- Commands Display --}}
        <div class="space-y-4">
            <div class="relative">
                <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="refreshCommands,filterStatus,filterType,filterPersonaId,dateRange">
                    Loading commands...
                </x-ui.loading-state>

                {{-- Grouped Commands Display --}}
                @if($groupBy !== 'none' && $groupedCommands)
                    @foreach($groupedCommands as $groupName => $groupCommands)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-4">
                            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                <h4 class="text-sm font-semibold text-gray-900 dark:text-white">
                                    {{ ucfirst($groupBy) }}: {{ ucfirst($groupName) }} ({{ count($groupCommands) }})
                                </h4>
                            </div>
                            <div class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($groupCommands as $command)
                                    @include('livewire.partials.command-card', ['command' => $command])
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                @else
                    {{-- Regular Commands Display --}}
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse($commands as $command)
                                @include('livewire.partials.command-card', ['command' => $command])
                            @empty
                                <div class="text-center py-12">
                                    <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                    <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No commands found</h3>
                                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                        @if($filterStatus || $filterType || $filterPersonaId)
                                            No commands match your current filters. Try adjusting your search criteria.
                                        @else
                                            No commands have been created yet for the selected time period.
                                        @endif
                                    </p>
                                    <div class="mt-6 flex flex-col sm:flex-row gap-2 justify-center">
                                        <x-ui.button
                                            wire:click="refreshCommands"
                                            wire:loading.attr="disabled"
                                            wire:target="refreshCommands"
                                            variant="secondary"
                                            size="sm">
                                            <div wire:loading.remove wire:target="refreshCommands">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                </svg>
                                                Refresh Data
                                            </div>
                                            <x-ui.loading-state type="inline" show wire:loading wire:target="refreshCommands" />
                                        </x-ui.button>
                                        @if($filterStatus || $filterType || $filterPersonaId)
                                            <x-ui.button
                                                wire:click="$set('filterStatus', ''); $set('filterType', ''); $set('filterPersonaId', null);"
                                                variant="primary"
                                                size="sm">
                                                Clear Filters
                                            </x-ui.button>
                                        @endif
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>



@push('scripts')
<script>
document.addEventListener('livewire:init', () => {
    // Command action notifications
    Livewire.on('commandDeleted', () => {
        window.dispatchEvent(new CustomEvent('notify', {
            detail: { message: 'Command deleted successfully', type: 'success' }
        }));
    });

    Livewire.on('commandRetried', () => {
        window.dispatchEvent(new CustomEvent('notify', {
            detail: { message: 'Command queued for retry', type: 'success' }
        }));
    });

    Livewire.on('error', (message) => {
        window.dispatchEvent(new CustomEvent('notify', {
            detail: { message: message, type: 'error' }
        }));
    });

    // Bulk action notifications
    Livewire.on('bulkRetried', (data) => {
        window.dispatchEvent(new CustomEvent('notify', {
            detail: { message: `${data.count} commands retried successfully`, type: 'success' }
        }));
    });

    Livewire.on('bulkDeleted', (data) => {
        window.dispatchEvent(new CustomEvent('notify', {
            detail: { message: `${data.count} commands deleted successfully`, type: 'success' }
        }));
    });
});

// Helper function for status badge classes
function getStatusClasses(status) {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        'processing': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        'success': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        'failed': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        'error': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
}
</script>
@endpush

