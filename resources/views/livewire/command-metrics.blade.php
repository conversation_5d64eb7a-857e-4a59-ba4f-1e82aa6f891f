<div class="space-y-6"
     x-data="{ isLoading: false }"
     wire:init="deferredLoad"
     wire:poll.60s="refreshMetrics">

    {{-- Header with Controls --}}
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div class="flex items-center gap-3">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Command Metrics</h2>
            <x-ui.tooltip text="Metrics are updated every 60 seconds">
                <x-ui.badge variant="info" size="sm">
                    Live
                </x-ui.badge>
            </x-ui.tooltip>
            @if(!empty($summaryMetrics))
                <x-ui.tooltip text="Overall system health status">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {{ $healthStatusColors[$summaryMetrics['health_status']] ?? $healthStatusColors['unknown'] }}">
                        {{ $healthStatusIcons[$summaryMetrics['health_status']] ?? $healthStatusIcons['unknown'] }}
                        {{ ucfirst($summaryMetrics['health_status']) }}
                    </span>
                </x-ui.tooltip>
            @endif
        </div>

        {{-- Controls --}}
        <div class="flex flex-wrap items-center gap-3 w-full sm:w-auto">
            {{-- Time Range --}}
            <div class="flex items-center gap-2">
                <label for="timeRange" class="text-sm font-medium text-gray-700 dark:text-gray-300">Period:</label>
                <select id="timeRange" wire:model.live="timeRange"
                        class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                        wire:loading.attr="disabled"
                        wire:target="timeRange,refreshMetrics"
                        aria-label="Select time range">
                    <option value="1h">Last Hour</option>
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                </select>
            </div>

            {{-- Group By --}}
            <div class="flex items-center gap-2">
                <label for="groupBy" class="text-sm font-medium text-gray-700 dark:text-gray-300">Group by:</label>
                <select id="groupBy" wire:model.live="groupBy"
                        class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                        wire:loading.attr="disabled"
                        wire:target="groupBy,refreshMetrics"
                        aria-label="Group by">
                    <option value="type">Command Type</option>
                    <option value="vps">VPS Server</option>
                    <option value="persona">Persona</option>
                    <option value="account">Account</option>
                </select>
            </div>

            {{-- Dynamic Filter based on Group By --}}
            @if($groupBy === 'type')
                <div class="flex items-center gap-2">
                    <label for="selectedType" class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</label>
                    <select id="selectedType" wire:model.live="selectedType"
                            class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            wire:loading.attr="disabled"
                            wire:target="selectedType,refreshMetrics"
                            aria-label="Select command type">
                        <option value="all">All Types</option>
                        @foreach($commandTypes as $type)
                            <option value="{{ $type }}">{{ ucfirst($type) }}</option>
                        @endforeach
                    </select>
                </div>
            @elseif($groupBy === 'vps')
                <div class="flex items-center gap-2">
                    <label for="selectedVpsId" class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</label>
                    <select id="selectedVpsId" wire:model.live="selectedVpsId"
                            class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            wire:loading.attr="disabled"
                            wire:target="selectedVpsId,refreshMetrics"
                            aria-label="Select VPS">
                        <option value="">All VPS</option>
                        @foreach($vpsOptions as $vps)
                            <option value="{{ $vps->id }}">{{ $vps->name }}</option>
                        @endforeach
                    </select>
                </div>
            @elseif($groupBy === 'persona')
                <div class="flex items-center gap-2">
                    <label for="selectedPersonaId" class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</label>
                    <select id="selectedPersonaId" wire:model.live="selectedPersonaId"
                            class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            wire:loading.attr="disabled"
                            wire:target="selectedPersonaId,refreshMetrics"
                            aria-label="Select Persona">
                        <option value="">All Personas</option>
                        @foreach($personaOptions as $persona)
                            <option value="{{ $persona->id }}">{{ $persona->name }}</option>
                        @endforeach
                    </select>
                </div>
            @elseif($groupBy === 'account')
                <div class="flex items-center gap-2">
                    <label for="selectedAccountId" class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</label>
                    <select id="selectedAccountId" wire:model.live="selectedAccountId"
                            class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            wire:loading.attr="disabled"
                            wire:target="selectedAccountId,refreshMetrics"
                            aria-label="Select Account">
                        <option value="">All Accounts</option>
                        @foreach($accountOptions as $account)
                            <option value="{{ $account->id }}">{{ $account->username }}</option>
                        @endforeach
                    </select>
                </div>
            @endif

            {{-- Refresh Button --}}
            <x-ui.button
                wire:click="refreshMetrics"
                wire:loading.attr="disabled"
                wire:target="refreshMetrics"
                variant="secondary"
                size="sm"
                class="ml-2 min-w-[5rem]">
                <div wire:loading.remove wire:target="refreshMetrics">
                    Refresh
                </div>
                <x-ui.loading-state type="inline" show wire:loading wire:target="refreshMetrics" />
            </x-ui.button>
        </div>
    </div>

    {{-- Error Messages --}}
    @error('error')
        <div role="alert" class="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg">
            {{ $message }}
        </div>
    @enderror

    {{-- Summary Metrics --}}
    @if(!empty($summaryMetrics) && $showSummary)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">System Overview</h3>
                <button wire:click="toggleSummary" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    Hide Summary
                </button>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                {{-- Total Commands --}}
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ number_format($summaryMetrics['total_commands']) }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Total Commands</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        {{ ucfirst(str_replace(['1h', '24h', '7d', '30d'], ['Last Hour', 'Last 24h', 'Last 7 Days', 'Last 30 Days'], $timeRange)) }}
                    </div>
                </div>

                {{-- Success Rate --}}
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $summaryMetrics['overall_success_rate'] >= 90 ? 'text-green-600' : ($summaryMetrics['overall_success_rate'] >= 75 ? 'text-yellow-600' : 'text-red-600') }}">
                        {{ number_format($summaryMetrics['overall_success_rate'], 1) }}%
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Success Rate</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        {{ number_format($summaryMetrics['total_successful']) }} / {{ number_format($summaryMetrics['total_failed']) }}
                    </div>
                </div>

                {{-- Avg Execution Time --}}
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $summaryMetrics['avg_execution_time'] <= 30 ? 'text-green-600' : ($summaryMetrics['avg_execution_time'] <= 60 ? 'text-yellow-600' : 'text-red-600') }}">
                        {{ number_format($summaryMetrics['avg_execution_time'], 1) }}s
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Avg. Time</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">Execution</div>
                </div>

                {{-- Most Active Type --}}
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ $summaryMetrics['most_active_type'] ? ucfirst($summaryMetrics['most_active_type']) : 'N/A' }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Most Active</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">Command Type</div>
                </div>
            </div>
        </div>
    @elseif($showSummary)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">System Overview</h3>
                <button wire:click="toggleSummary" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    Hide Summary
                </button>
            </div>
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <div class="text-sm">No summary data available for the selected time period.</div>
            </div>
        </div>
    @else
        <div class="mb-4">
            <button wire:click="toggleSummary" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                Show System Overview
            </button>
        </div>
    @endif

    {{-- Loading State --}}
    <div class="relative">
        <x-ui.loading-state type="overlay" show="true" wire:loading.delay wire:target="deferredLoad,groupBy" message="Loading metrics..." />

        {{-- Metrics Grid --}}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($metrics as $metric)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 relative transition-all duration-200 hover:shadow-md">
                    {{-- Card Header --}}
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                @if($groupBy === 'type')
                                    {{ ucfirst($metric['command_type']) }} Commands
                                @elseif($groupBy === 'vps')
                                    {{ $vpsOptions->firstWhere('id', $metric['vps_id'])?->name ?? 'Unknown VPS' }}
                                @elseif($groupBy === 'persona')
                                    {{ $personaOptions->firstWhere('id', $metric['persona_id'])?->name ?? 'Unknown Persona' }}
                                @else
                                    {{ $accountOptions->firstWhere('id', $metric['account_id'])?->username ?? 'Unknown Account' }}
                                @endif
                            </h3>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                @if($groupBy === 'type')
                                    System-wide {{ $metric['command_type'] }} performance
                                @elseif($groupBy === 'vps')
                                    VPS performance metrics
                                @elseif($groupBy === 'persona')
                                    Persona activity metrics
                                @else
                                    Account performance metrics
                                @endif
                            </div>
                        </div>
                        <div class="flex flex-col items-end">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border {{ $healthStatusColors[$metric['status_health']] ?? $healthStatusColors['unknown'] }}">
                                {{ $healthStatusIcons[$metric['status_health']] ?? $healthStatusIcons['unknown'] }}
                                {{ ucfirst($metric['status_health']) }}
                            </span>
                            <span class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                {{ number_format($metric['total_commands']) }} total
                            </span>
                        </div>
                    </div>

                    {{-- Key Metrics Row --}}
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">
                                {{ number_format($metric['successful_commands']) }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Success</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-red-600">
                                {{ number_format($metric['failed_commands']) }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Failed</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold {{ $metric['avg_execution_time'] <= 30 ? 'text-green-600' : ($metric['avg_execution_time'] <= 60 ? 'text-yellow-600' : 'text-red-600') }}">
                                {{ number_format($metric['avg_execution_time'], 1) }}s
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Avg Time</div>
                        </div>
                    </div>

                    {{-- Success Rate Progress Bar --}}
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Success Rate</span>
                            <span class="text-sm font-semibold tabular-nums {{ $metric['success_rate'] >= 90 ? 'text-green-600' : ($metric['success_rate'] >= 75 ? 'text-yellow-600' : 'text-red-600') }}">
                                {{ number_format($metric['success_rate'], 1) }}%
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                            <div class="h-2 rounded-full transition-all duration-500 ease-out {{ $metric['success_rate'] >= 90 ? 'bg-green-500' : ($metric['success_rate'] >= 75 ? 'bg-yellow-500' : 'bg-red-500') }}"
                                 style="width: {{ $metric['success_rate'] }}%"
                                 role="progressbar"
                                 aria-valuenow="{{ $metric['success_rate'] }}"
                                 aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>

                    {{-- Top Errors (if any) --}}
                    @if(!empty($metric['top_errors']))
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Common Issues</h4>
                            <div class="space-y-2">
                                @foreach($metric['top_errors'] as $error)
                                    <div class="flex items-start justify-between text-xs">
                                        <x-ui.tooltip :text="$error['message']">
                                            <span class="text-gray-600 dark:text-gray-400 truncate flex-1 leading-relaxed">
                                                {{ \Illuminate\Support\Str::limit($error['message'], 35) }}
                                            </span>
                                        </x-ui.tooltip>
                                        <span class="ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            {{ $error['count'] }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="text-center text-sm text-green-600 dark:text-green-400">
                                ✓ No errors recorded
                            </div>
                        </div>
                    @endif
                </div>
            @empty
                <div class="col-span-full">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                        <div class="text-center">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Metrics Available</h3>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">
                                No command metrics found for the selected time period and filters.
                                @if($groupBy !== 'type')
                                    <br>Try switching to "By Command Type" or selecting a different time range.
                                @endif
                            </p>
                            <div class="flex flex-col sm:flex-row gap-2 justify-center">
                                <x-ui.button
                                    wire:click="refreshMetrics"
                                    wire:loading.attr="disabled"
                                    wire:target="refreshMetrics"
                                    variant="secondary"
                                    size="sm">
                                    <div wire:loading.remove wire:target="refreshMetrics">
                                        Refresh Data
                                    </div>
                                    <x-ui.loading-state type="inline" show wire:loading wire:target="refreshMetrics" />
                                </x-ui.button>
                                @if($groupBy !== 'type')
                                    <x-ui.button
                                        wire:click="$set('groupBy', 'type')"
                                        variant="primary"
                                        size="sm">
                                        View by Command Type
                                    </x-ui.button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('livewire:init', () => {
    Livewire.on('metricsLoaded', () => {
        Alpine.store('metrics').isLoading = false;
    });
});
</script>
@endpush