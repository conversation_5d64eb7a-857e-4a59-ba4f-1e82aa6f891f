version: '3.9'

services:

  nginx:
    build: docker/nginx
    container_name: ${APP_NAME}_nginx
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: on-failure
    volumes:
      - ./:/var/www/html/
    ports:
      - "80:80"
    working_dir: /var/www/html/
    networks:
      spore_network:
        aliases: [www]
    depends_on:
      - php-fpm
      - mysql

  php-fpm:
    build: docker/php-fpm
    container_name: ${APP_NAME}_php
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: on-failure
    volumes:
      - ./:/var/www/html/
    working_dir: /var/www/html/
    networks:
      - spore_network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4096M
  
  mysql:
    image: mariadb:latest
    container_name: ${APP_NAME}_mysql
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: on-failure
    volumes:
      - mysqldata:/var/lib/mysql
      - ./dump:/var/lib/mysql/dump
    networks:
      - spore_network
    environment:
      - MYSQL_DATABASE=${DB_DATABASE}
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}

  redis:
    image: redis:latest
    container_name: ${APP_NAME}_redis
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: on-failure
    volumes:
      - redis-volume:/data
    networks:
      - spore_network

  npm:
    build: docker/npm
    container_name: ${APP_NAME}_npm
    extra_hosts:
      - host.docker.internal:host-gateway
    volumes:
      - ./:/var/www/html
    working_dir: /var/www/html
    entrypoint: ['npm', '--no-bin-links']
    networks:
      - spore_network

  laravel-worker-1:
    build: docker/php-fpm
    container_name: ${APP_NAME}_laravel-worker-1
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: unless-stopped
    volumes:
        - ./:/var/www/html/
    working_dir: /var/www/html/
    command: php artisan queue:work --queue=default --tries=1 --max-time=3600 --timeout=180 -v
    networks:
        - spore_network

volumes:
  mysqldata:
  redis-volume:

networks:
  spore_network:
    name: ${APP_NAME}_network
